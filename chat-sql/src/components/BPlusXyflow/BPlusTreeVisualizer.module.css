/* CSS变量定义 */
:root {
  /* 亮色主题 */
  --bpt-bg-light: #ffffff;
  --bpt-text-light: #333333;
  --bpt-internal-node-bg-light: #e0f2f7; /* 浅蓝色 */
  --bpt-leaf-node-bg-light: #fbe9e7; /* 浅红色 */
  --bpt-cell-border-light: #999999;
  --bpt-handle-bg-light: #4caf50; /* 绿色 */
  --bpt-key-bg-light: #f5f5f5;
  --bpt-value-bg-light: #fff3e0;
  --bpt-pointer-bg-light: #e8f5e8;
  --bpt-shadow-light: rgba(0, 0, 0, 0.1);
  --bpt-border-light: #ddd;
  --bpt-info-bg-light: #f9f9f9;
}

.dark-mode {
  /* 暗色主题 */
  --bpt-bg-dark: #222222;
  --bpt-text-dark: #f0f0f0;
  --bpt-internal-node-bg-dark: #37474f; /* 深蓝灰色 */
  --bpt-leaf-node-bg-dark: #4e342e; /* 深棕色 */
  --bpt-cell-border-dark: #666666;
  --bpt-handle-bg-dark: #81c784; /* 浅绿色 */
  --bpt-key-bg-dark: #2a2a2a;
  --bpt-value-bg-dark: #3a2f2a;
  --bpt-pointer-bg-dark: #2a3a2a;
  --bpt-shadow-dark: rgba(0, 0, 0, 0.3);
  --bpt-border-dark: #555;
  --bpt-info-bg-dark: #2a2a2a;
}

/* 主容器 */
.bplus-visualizer {
  width: 100%;
  height: 100vh;
  position: relative;
  background: var(--bpt-bg-light);
  color: var(--bpt-text-light);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.bplus-visualizer.dark-mode {
  background: var(--bpt-bg-dark);
  color: var(--bpt-text-dark);
}

/* 内部节点样式 */
.bplus-internal-node {
  background: var(--bpt-internal-node-bg-light);
  border: 2px solid var(--bpt-cell-border-light);
  border-radius: 8px;
  padding: 2px;
  box-shadow: 0 2px 8px var(--bpt-shadow-light);
  transition: all 0.3s ease;
}

.dark-mode .bplus-internal-node {
  background: var(--bpt-internal-node-bg-dark);
  border-color: var(--bpt-cell-border-dark);
  box-shadow: 0 2px 8px var(--bpt-shadow-dark);
}

.bplus-internal-node:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px var(--bpt-shadow-light);
}

.dark-mode .bplus-internal-node:hover {
  box-shadow: 0 4px 12px var(--bpt-shadow-dark);
}

/* 叶子节点样式 */
.bplus-leaf-node {
  background: var(--bpt-leaf-node-bg-light);
  border: 2px solid var(--bpt-cell-border-light);
  border-radius: 8px;
  padding: 2px;
  box-shadow: 0 2px 8px var(--bpt-shadow-light);
  transition: all 0.3s ease;
}

.dark-mode .bplus-leaf-node {
  background: var(--bpt-leaf-node-bg-dark);
  border-color: var(--bpt-cell-border-dark);
  box-shadow: 0 2px 8px var(--bpt-shadow-dark);
}

.bplus-leaf-node:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px var(--bpt-shadow-light);
}

.dark-mode .bplus-leaf-node:hover {
  box-shadow: 0 4px 12px var(--bpt-shadow-dark);
}

/* 节点内容 */
.bplus-node-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* 移除节点头部样式，不再需要 */

/* 内部节点布局 */
.bplus-internal-layout {
  display: flex;
  align-items: center;
  gap: 0px;
  justify-content: center;
  position: relative;
  padding: 0px;
}

/* 移除指针位置样式，不再需要 */

/* 叶子节点布局 */
.bplus-leaf-layout {
  display: flex;
  flex-direction: column;
  gap: 0px;
  padding: 0px;
}

/* 键行布局 */
.bplus-key-row {
  display: flex;
  gap: 2px;
  justify-content: center;
}

/* 槽位容器 */
.bplus-slot-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.dark-mode .bplus-slot-container {
  border-left-color: var(--bpt-cell-border-dark);
}

/* 单个槽位样式 */
.bplus-slot {
  position: relative;
  min-width: 60px;
  height: 45px;
  border-right: 2px solid var(--bpt-cell-border-light);
  border-top: 2px solid var(--bpt-cell-border-light);
  border-bottom: 2px solid var(--bpt-cell-border-light);
  background: var(--bpt-key-bg-light);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px var(--bpt-shadow-light);
}

.dark-mode .bplus-slot {
  border-right-color: var(--bpt-cell-border-dark);
  border-top-color: var(--bpt-cell-border-dark);
  border-bottom-color: var(--bpt-cell-border-dark);
  background: var(--bpt-key-bg-dark);
  box-shadow: 0 1px 3px var(--bpt-shadow-dark);
}

.bplus-slot:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 6px var(--bpt-shadow-light);
}

.dark-mode .bplus-slot:hover {
  box-shadow: 0 3px 6px var(--bpt-shadow-dark);
}

/* 空槽位样式 */
.bplus-slot-empty {
  background: var(--bpt-bg-light);
  border-style: dashed;
  opacity: 0.6;
}

.dark-mode .bplus-slot-empty {
  background: var(--bpt-bg-dark);
}

/* 槽位内容 */
.bplus-slot-content {
  font-size: 16px;
  font-weight: bold;
  color: var(--bpt-text-light);
  -webkit-user-select: none;
  user-select: none;
}

.dark-mode .bplus-slot-content {
  color: var(--bpt-text-dark);
}

/* 键槽位 */
.bplus-key-cell {
  position: relative;
  min-width: 50px;
  height: 40px;
  border: 2px solid var(--bpt-cell-border-light);
  border-radius: 6px;
  background: var(--bpt-key-bg-light);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.dark-mode .bplus-key-cell {
  border-color: var(--bpt-cell-border-dark);
  background: var(--bpt-key-bg-dark);
}

.bplus-key-cell:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px var(--bpt-shadow-light);
}

.dark-mode .bplus-key-cell:hover {
  box-shadow: 0 2px 4px var(--bpt-shadow-dark);
}

.bplus-key-content {
  font-size: 16px;
  font-weight: bold;
  color: var(--bpt-text-light);
}

.dark-mode .bplus-key-content {
  color: var(--bpt-text-dark);
}

/* 移除值槽位样式，不再需要 */

/* 指针槽位 */
.bplus-pointer-cell {
  position: relative;
  min-width: 30px;
  height: 30px;
  border: 1px solid var(--bpt-cell-border-light);
  border-radius: 4px;
  background: var(--bpt-pointer-bg-light);
  display: flex;
  align-items: center;
  justify-content: center;
}

.dark-mode .bplus-pointer-cell {
  border-color: var(--bpt-cell-border-dark);
  background: var(--bpt-pointer-bg-dark);
}

.bplus-pointer-content {
  font-size: 16px;
  color: var(--bpt-handle-bg-light);
  font-weight: bold;
}

.dark-mode .bplus-pointer-content {
  color: var(--bpt-handle-bg-dark);
}

/* 兄弟指针区域 */
.bplus-sibling-pointer {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  position: relative;
}

.dark-mode .bplus-sibling-pointer {
  background: rgba(0, 0, 0, 0.2);
}

.bplus-sibling-label {
  font-size: 10px;
  color: #666;
}

.dark-mode .bplus-sibling-label {
  color: #aaa;
}

.bplus-sibling-content {
  font-size: 16px;
  color: var(--bpt-handle-bg-light);
  font-weight: bold;
}

.dark-mode .bplus-sibling-content {
  color: var(--bpt-handle-bg-dark);
}

/* 移除节点信息样式，不再需要 */

/* Handle样式 */
.bplus-handle {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  
  border: none !important;
  background: transparent !important;
}

.dark-mode .bplus-handle {
  border-color: var(--bpt-handle-bg-dark);
  background: var(--bpt-handle-bg-dark);
}

.bplus-handle-target {
  background: white;
}

.dark-mode .bplus-handle-target {
  background: #333;
}

.bplus-handle-sibling {
  background: #ff9800;
  border-color: #ff9800;
}

/* 信息面板 */
.bplus-info-panel {
  position: absolute;
  top: 10px;
  right: 10px;
  background: var(--bpt-info-bg-light);
  border: 1px solid var(--bpt-border-light);
  border-radius: 8px;
  padding: 12px;
  min-width: 200px;
  box-shadow: 0 2px 8px var(--bpt-shadow-light);
  z-index: 1000;
}

.dark-mode .bplus-info-panel {
  background: var(--bpt-info-bg-dark);
  border-color: var(--bpt-border-dark);
  box-shadow: 0 2px 8px var(--bpt-shadow-dark);
}

.bplus-tree-stats h3 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: var(--bpt-text-light);
}

.dark-mode .bplus-tree-stats h3 {
  color: var(--bpt-text-dark);
}

.bplus-tree-stats p {
  margin: 4px 0;
  font-size: 12px;
  color: #666;
}

.dark-mode .bplus-tree-stats p {
  color: #aaa;
}

/* React Flow控件样式 */
.bplus-controls {
  background: var(--bpt-info-bg-light);
  border: 1px solid var(--bpt-border-light);
  border-radius: 8px;
}

.dark-mode .bplus-controls {
  background: var(--bpt-info-bg-dark);
  border-color: var(--bpt-border-dark);
}

.bplus-minimap {
  background: var(--bpt-info-bg-light);
  border: 1px solid var(--bpt-border-light);
  border-radius: 8px;
}

.dark-mode .bplus-minimap {
  background: var(--bpt-info-bg-dark);
  border-color: var(--bpt-border-dark);
}

.bplus-background {
  background: var(--bpt-bg-light);
}

.dark-mode .bplus-background {
  background: var(--bpt-bg-dark);
}
