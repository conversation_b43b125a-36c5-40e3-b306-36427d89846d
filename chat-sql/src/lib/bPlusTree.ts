// B+树动画步骤类型定义
export type AnimationStep = 
  | { type: 'traverse', nodeId: string, path: string[] }
  | { type: 'insert_key', nodeId: string, key: number }
  | { type: 'split', originalNodeId: string, newNodeId: string, promotedKey: number }
  | { type: 'delete_key', nodeId: string, key: number }
  | { type: 'merge', nodeId1: string, nodeId2: string, resultNodeId: string }
  | { type: 'redistribute', fromNodeId: string, toNodeId: string, key: number }
  | { type: 'update_parent', nodeId: string, newKey: number };

// B+树节点接口
export interface BPlusTreeNode {
  id: string;
  keys: number[];
  pointers: (string | null)[];
  isLeaf: boolean;
  level: number;
  parent?: string | null;
  next?: string | null; // 叶子节点的兄弟指针
}

// B+树类
export class BPlusTree {
  private order: number;
  private root: BPlusTreeNode | null = null;
  private nodeCounter = 0;
  private allNodes: Map<string, BPlusTreeNode> = new Map();

  constructor(order: number = 3) {
    this.order = order;
  }

  // 创建新节点
  private createNode(isLeaf: boolean, level: number): BPlusTreeNode {
    const node: BPlusTreeNode = {
      id: `node-${this.nodeCounter++}`,
      keys: [],
      pointers: [],
      isLeaf,
      level,
      parent: null,
      next: null
    };

    this.allNodes.set(node.id, node);
    return node;
  }

  // 查找叶子节点
  private *findLeafNode(key: number): Generator<AnimationStep, BPlusTreeNode, unknown> {
    if (!this.root) {
      throw new Error('树为空');
    }

    let current = this.root;
    const path: string[] = [current.id];

    while (!current.isLeaf) {
      yield { type: 'traverse', nodeId: current.id, path: [...path] };

      // 找到合适的子节点
      let childIndex = 0;
      while (childIndex < current.keys.length && key >= current.keys[childIndex]) {
        childIndex++;
      }

      const childId = current.pointers[childIndex];
      if (!childId || !this.allNodes.has(childId)) {
        throw new Error('无效的子节点指针');
      }

      current = this.allNodes.get(childId)!;
      path.push(current.id);
    }

    yield { type: 'traverse', nodeId: current.id, path: [...path] };
    return current;
  }

  // 插入键值的生成器函数
  public *insert(key: number): Generator<AnimationStep, void, unknown> {
    // 如果树为空，创建根节点
    if (!this.root) {
      this.root = this.createNode(true, 0);
      this.root.keys.push(key);
      yield { type: 'insert_key', nodeId: this.root.id, key };
      return;
    }

    // 找到要插入的叶子节点
    const leafNode = yield* this.findLeafNode(key);

    // 检查键是否已存在
    if (leafNode.keys.includes(key)) {
      throw new Error(`键 ${key} 已存在`);
    }

    // 在叶子节点中插入键
    yield { type: 'insert_key', nodeId: leafNode.id, key };
    this.insertKeyIntoNode(leafNode, key);

    // 检查是否需要分裂
    if (leafNode.keys.length >= this.order) {
      yield* this.splitLeafNode(leafNode);
    }
  }

  // 在节点中插入键（保持有序）
  private insertKeyIntoNode(node: BPlusTreeNode, key: number): void {
    let insertIndex = 0;
    while (insertIndex < node.keys.length && node.keys[insertIndex] < key) {
      insertIndex++;
    }
    node.keys.splice(insertIndex, 0, key);
  }

  // 分裂叶子节点
  private *splitLeafNode(node: BPlusTreeNode): Generator<AnimationStep, void, unknown> {
    const mid = Math.ceil(node.keys.length / 2);
    const newNode = this.createNode(true, node.level);
    
    // 分配键
    newNode.keys = node.keys.splice(mid);
    
    // 更新兄弟指针
    newNode.next = node.next;
    node.next = newNode.id;
    
    // 提升的键是新节点的第一个键
    const promotedKey = newNode.keys[0];
    
    yield { 
      type: 'split', 
      originalNodeId: node.id, 
      newNodeId: newNode.id, 
      promotedKey 
    };

    // 如果是根节点，创建新的根节点
    if (!node.parent) {
      const newRoot = this.createNode(false, node.level + 1);
      newRoot.keys.push(promotedKey);
      newRoot.pointers.push(node.id, newNode.id);
      
      node.parent = newRoot.id;
      newNode.parent = newRoot.id;
      this.root = newRoot;
    } else {
      // 向父节点插入提升的键
      const parent = this.allNodes.get(node.parent)!;
      newNode.parent = node.parent;
      yield* this.insertIntoInternalNode(parent, promotedKey, newNode.id);
    }
  }

  // 向内部节点插入键
  private *insertIntoInternalNode(
    node: BPlusTreeNode, 
    key: number, 
    rightChildId: string
  ): Generator<AnimationStep, void, unknown> {
    yield { type: 'insert_key', nodeId: node.id, key };
    
    // 找到插入位置
    let insertIndex = 0;
    while (insertIndex < node.keys.length && node.keys[insertIndex] < key) {
      insertIndex++;
    }
    
    // 插入键和指针
    node.keys.splice(insertIndex, 0, key);
    node.pointers.splice(insertIndex + 1, 0, rightChildId);

    // 检查是否需要分裂
    if (node.keys.length >= this.order) {
      yield* this.splitInternalNode(node);
    }
  }

  // 分裂内部节点
  private *splitInternalNode(node: BPlusTreeNode): Generator<AnimationStep, void, unknown> {
    const mid = Math.floor(node.keys.length / 2);
    const newNode = this.createNode(false, node.level);
    
    // 提升中间键
    const promotedKey = node.keys[mid];
    
    // 分配键和指针
    newNode.keys = node.keys.splice(mid + 1);
    newNode.pointers = node.pointers.splice(mid + 1);
    node.keys.splice(mid, 1); // 移除提升的键
    
    // 更新子节点的父指针
    newNode.pointers.forEach(pointerId => {
      if (pointerId) {
        const child = this.allNodes.get(pointerId);
        if (child) {
          child.parent = newNode.id;
        }
      }
    });
    
    yield { 
      type: 'split', 
      originalNodeId: node.id, 
      newNodeId: newNode.id, 
      promotedKey 
    };

    // 如果是根节点，创建新的根节点
    if (!node.parent) {
      const newRoot = this.createNode(false, node.level + 1);
      newRoot.keys.push(promotedKey);
      newRoot.pointers.push(node.id, newNode.id);
      
      node.parent = newRoot.id;
      newNode.parent = newRoot.id;
      this.root = newRoot;
    } else {
      // 向父节点插入提升的键
      const parent = this.allNodes.get(node.parent)!;
      newNode.parent = node.parent;
      yield* this.insertIntoInternalNode(parent, promotedKey, newNode.id);
    }
  }

  // 删除键值的生成器函数
  public *delete(key: number): Generator<AnimationStep, void, unknown> {
    if (!this.root) {
      throw new Error('树为空');
    }

    // 找到包含键的叶子节点
    const leafNode = yield* this.findLeafNode(key);

    // 检查键是否存在
    const keyIndex = leafNode.keys.indexOf(key);
    if (keyIndex === -1) {
      throw new Error(`键 ${key} 不存在`);
    }

    // 从叶子节点删除键
    yield { type: 'delete_key', nodeId: leafNode.id, key };
    leafNode.keys.splice(keyIndex, 1);

    // 检查是否需要重新平衡
    const minKeys = Math.ceil((this.order - 1) / 2);
    if (leafNode.keys.length < minKeys && leafNode !== this.root) {
      yield* this.rebalanceAfterDeletion(leafNode);
    }
  }

  // 删除后重新平衡
  private *rebalanceAfterDeletion(node: BPlusTreeNode): Generator<AnimationStep, void, unknown> {
    const minKeys = Math.ceil((this.order - 1) / 2);
    
    if (node.keys.length >= minKeys) {
      return; // 不需要重新平衡
    }

    // TODO: 实现重新分配和合并逻辑
    // 这里简化处理，实际应该包含从兄弟节点借键或合并节点的逻辑
    console.log('需要重新平衡节点:', node.id);
  }

  // 获取所有节点（用于可视化）
  public getAllNodes(): BPlusTreeNode[] {
    return Array.from(this.allNodes.values());
  }

  // 获取根节点
  public getRoot(): BPlusTreeNode | null {
    return this.root;
  }

  // 清空树
  public clear(): void {
    this.root = null;
    this.allNodes.clear();
    this.nodeCounter = 0;
  }
}
